CalendarView As screen:
    Fill: =RGBA(255,255,255,1)
    LoadingSpinnerColor: =RGBA(211, 66, 9, 1)
    OnVisible: |-
        =// Set the first day of the current week (Sunday)
        Set(_firstDayOfWeek, DateAdd(Today(), -(Weekday(Today()) - 1), TimeUnit.Days));

        // Load time off requests from a wide (but delegable) date range into a local collection.
        // This filter on a SINGLE date column is fully delegable.
        ClearCollect(
            colTimeOff,
            Filter(
                TimeOffCS,
                'Leave From' >= DateAdd(Today(), -1, TimeUnit.Months) &&
                'Leave From' <= DateAdd(Today(), 6, TimeUnit.Months)
            )
        );

    LblWeekTitle As label:
        Align: =Align.Center
        Height: =42
        Size: =24
        Text: =Text(_firstDayOfWeek, "mmm dd") & " - " & Text(DateAdd(_firstDayOfWeek, 6, TimeUnit.Days), "mmm dd, yyyy")
        Width: =386
        X: =324
        ZIndex: =1

    IconPreviousWeek As icon.ChevronLeft:
        Height: =32
        Icon: =Icon.ChevronLeft
        OnSelect: =Set(_firstDayOfWeek, DateAdd(_firstDayOfWeek, -7, TimeUnit.Days))
        Width: =50
        X: =351
        Y: =6
        ZIndex: =2

    IconNextWeek As icon.ChevronRight:
        Height: =32
        Icon: =Icon.ChevronRight
        OnSelect: =Set(_firstDayOfWeek, DateAdd(_firstDayOfWeek, 7, TimeUnit.Days))
        Width: =50
        X: =643
        Y: =10
        ZIndex: =3

    GalleryDayHeaders As gallery.galleryHorizontal:
        Height: =58
        Items: =["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
        TemplatePadding: =0
        TemplateSize: =Self.Width / 7
        Width: =1058
        Y: =42
        ZIndex: =4

        Label3 As label:
            Align: =Align.Center
            Height: =Parent.TemplateHeight
            OnSelect: =Select(Parent)
            Size: =16
            Text: =ThisItem.Value
            Width: =Parent.TemplateWidth
            ZIndex: =1

    galCalendar_1 As gallery.galleryHorizontal:
        '#CopilotOverlayLabel': ="Filtered"
        BorderColor: =RGBA(131, 24, 75, 1)
        DelayItemLoading: =true
        Height: =528
        Items: =Sequence(7)
        Layout: =Layout.Horizontal
        LoadingSpinner: =LoadingSpinner.Data
        TemplateSize: =152.7
        Width: =1069
        X: =57
        Y: =111
        ZIndex: =5

        Label18_1 As label:
            BorderColor: =RGBA(131, 24, 75, 1)
            Color: =RGBA(0, 0, 0, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            Height: =30
            HoverColor: =RGBA(131, 24, 75, 1)
            OnSelect: |
                =Set(
                    varSelectedDate,
                    DateAdd(_firstDayOfWeek, ThisItem.Value - 1, TimeUnit.Days)
                );
                Navigate(
                    DateDetails,
                    ScreenTransition.Fade
                )
            Size: =21
            Text: =Day(DateAdd(_firstDayOfWeek, ThisItem.Value - 1, TimeUnit.Days))
            Width: =Parent.TemplateWidth
            Y: =5
            ZIndex: =1

        LblRestrictionInfo As label:
            BorderColor: =RGBA(131, 24, 75, 1)
            Color: =RGBA(211, 66, 9, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            Height: =22
            OnSelect: =Select(Parent)
            Size: =9
            Text: |
                =With(
                    {
                        CurrentDate: DateAdd(_firstDayOfWeek, ThisItem.Value - 1, TimeUnit.Days)
                    },
                    Concat(
                        Filter(
                            Calendar_Restriction_CSU,
                            DateValue(Date) = DateValue(CurrentDate)
                        ),
                        If(
                            !IsBlank(Events.Value),
                            Events.Value,
                            Title
                        ) & " (" & TypeofEvent.Value & ")",
                        Char(10)
                    )
                )
            Width: =Parent.TemplateWidth
            Y: =35
            ZIndex: =2

        LblAbsenteeCount As label:
            BorderColor: =RGBA(131, 24, 75, 1)
            Color: =RGBA(131, 24, 75, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            Height: =80
            OnSelect: =Select(Parent)
            PaddingBottom: =2
            PaddingRight: =2
            Size: =8
            Text: |-
                =With(
                    {
                        CurrentDate: DateAdd(_firstDayOfWeek, ThisItem.Value - 1, TimeUnit.Days)
                    },
                        With(
                        {
                            NextDayStart: DateAdd(CurrentDate, 1)
                        },
                    Concat(
                        AddColumns(
                            GroupBy(
                                Filter(
                                    colTimeOff,
                                    'Leave From' < NextDayStart  &&
                                    'Leave To' >= CurrentDate
                                ),
                                Role,
                                RoleGroup
                            ),
                            Count,
                            CountRows(RoleGroup)
                        ),
                        Role & ": " & Count,
                        Char(10)
                    )
                )
                )
            Width: =Parent.TemplateWidth
            Y: =35
            ZIndex: =3

    BackToEnterData As button:
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(56, 96, 178, 1)
        FontWeight: =FontWeight.Semibold
        Height: =32
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(56, 96, 178, 1), -20%)
        OnSelect: |
            =Navigate(
              If(
                IsBlank(varPreviousScreen),
                EnterData,
                Switch(
                  varPreviousScreen,
                  "EnterData", EnterData,
                  "EditRecordScreen", EditRecordScreen,
                  EnterData
                )
              ),
              ScreenTransition.Fade
            )
        Size: =14
        Text: ="Back"
        Width: =100
        X: =20
        Y: =10
        ZIndex: =6

    RestrictionAddCalendar As button:
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(211, 66, 9, 1)
        FontWeight: =FontWeight.Semibold
        Height: =32
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        OnSelect: |
            =// Clear global variables for add mode
            Set(gblRestrictionToEdit, Blank());
            Set(varIsEditMode, false);
            Navigate(
              DateRestriction,
              ScreenTransition.Fade
            )
        Size: =14
        Text: ="Add Restriction"
        Width: =150
        X: =950
        Y: =10
        ZIndex: =7