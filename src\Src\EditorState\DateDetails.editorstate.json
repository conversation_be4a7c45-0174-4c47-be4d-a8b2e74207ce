{"ControlStates": {"BackToCalendar": {"AllowAccessToGlobals": true, "ControlPropertyState": ["DisabledBorderColor", "DisabledColor", "DisabledFill", "Fill", "FontWeight", "Height", "HoverColor", "HoverFill", "OnSelect", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "BackToCalendar", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "", "Type": "ControlInfo"}, "BtnDeleteRestriction": {"AllowAccessToGlobals": true, "ControlPropertyState": ["BorderColor", "Color", "DisabledBorderColor", "DisabledColor", "DisabledFill", "Fill", "FontWeight", "Height", "HoverColor", "HoverFill", "OnSelect", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "AutoDisableOnSelect", "<PERSON><PERSON><PERSON>", "ContentLanguage", "RadiusTopLeft", "RadiusTopRight", "RadiusBottomLeft", "RadiusBottomRight", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "PressedColor", "DisplayMode", "PressedFill", "Font", "Italic", "Underline", "Strikethrough", "Align", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Visible", "VerticalAlign", "TabIndex", "maximumHeight", "maximumWidth", "minimumHeight", "minimumWidth"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "BtnDeleteRestriction", "OptimizeForDevices": "Off", "ParentIndex": 3, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoDisableOnSelect", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultButtonStyle", "Type": "ControlInfo"}, "BtnEditRestriction": {"AllowAccessToGlobals": true, "ControlPropertyState": ["BorderColor", "Color", "DisabledBorderColor", "DisabledColor", "DisabledFill", "Fill", "FontWeight", "Height", "HoverColor", "HoverFill", "OnSelect", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "AutoDisableOnSelect", "<PERSON><PERSON><PERSON>", "ContentLanguage", "RadiusTopLeft", "RadiusTopRight", "RadiusBottomLeft", "RadiusBottomRight", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "PressedColor", "DisplayMode", "PressedFill", "Font", "Italic", "Underline", "Strikethrough", "Align", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Visible", "VerticalAlign", "TabIndex", "maximumHeight", "maximumWidth", "minimumHeight", "minimumWidth"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "BtnEditRestriction", "OptimizeForDevices": "Off", "ParentIndex": 2, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoDisableOnSelect", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultButtonStyle", "Type": "ControlInfo"}, "DateDetails": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "LoadingSpinnerColor", "Height", "<PERSON><PERSON><PERSON>", "Size", "Orientation", "LoadingSpinner", "ImagePosition"], "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "DateDetails", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinnerColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Orientation", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ImagePosition", "RuleProviderType": "Unknown"}], "StyleName": "defaultScreenStyle", "Type": "ControlInfo"}, "GalAbsentees": {"AllowAccessToGlobals": true, "ControlPropertyState": ["BorderColor", "BorderThickness", "DelayItemLoading", "Height", "Items", "Layout", "LoadingSpinner", "TemplatePadding", "TemplateSize", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Fill"], "GalleryTemplateChildName": "GalAbsenteestemplate1", "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "GalAbsentees", "OptimizeForDevices": "Off", "ParentIndex": 6, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DelayItemLoading", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Layout", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplatePadding", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplateSize", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "", "Type": "ControlInfo"}, "GalAbsenteestemplate1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["ItemAccessibleLabel", "TemplateFill", "OnSelect"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": true, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "GalAbsenteestemplate1", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "ItemAccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplateFill", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultGalleryTemplateStyle", "Type": "ControlInfo"}, "GalRestrictions": {"AllowAccessToGlobals": true, "ControlPropertyState": ["BorderColor", "BorderThickness", "DelayItemLoading", "Height", "Items", "Layout", "LoadingSpinner", "TemplatePadding", "TemplateSize", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Fill"], "GalleryTemplateChildName": "GalRestrictionstemplate1", "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "GalRestrictions", "OptimizeForDevices": "Off", "ParentIndex": 3, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DelayItemLoading", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Layout", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplatePadding", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplateSize", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}], "StyleName": "", "Type": "ControlInfo"}, "GalRestrictionstemplate1": {"AllowAccessToGlobals": true, "ControlPropertyState": ["ItemAccessibleLabel", "TemplateFill", "OnSelect"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": true, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "GalRestrictionstemplate1", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "ItemAccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TemplateFill", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultGalleryTemplateStyle", "Type": "ControlInfo"}, "LblAbsenteesHeader": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "FontWeight", "Height", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "OnSelect", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblAbsenteesHeader", "OptimizeForDevices": "Off", "ParentIndex": 5, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblEmployeeName": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "FontWeight", "Height", "OnSelect", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblEmployeeName", "OptimizeForDevices": "Off", "ParentIndex": 2, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblEmployeeRole": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "FontWeight", "Height", "OnSelect", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblEmployeeRole", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblLeaveDates": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "Height", "OnSelect", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblLeaveDates", "OptimizeForDevices": "Off", "ParentIndex": 3, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblLeaveType": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "Height", "OnSelect", "Size", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "ThisItem.'Leave Type'", "InvariantPropertyName": "Text", "IsLockable": false, "NameMapSourceSchema": "?"}, "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblLeaveType", "OptimizeForDevices": "Off", "ParentIndex": 4, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblNoAbsentees": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "Height", "Size", "Text", "Visible", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "DisplayMode", "TabIndex", "OnSelect", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblNoAbsentees", "OptimizeForDevices": "Off", "ParentIndex": 7, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblNoRestrictions": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "Height", "Size", "Text", "Visible", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "DisplayMode", "TabIndex", "OnSelect", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblNoRestrictions", "OptimizeForDevices": "Off", "ParentIndex": 4, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblRestrictionEvent": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "FontWeight", "Height", "OnSelect", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblRestrictionEvent", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblRestrictionsHeader": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "FontWeight", "Height", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "OnSelect", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblRestrictionsHeader", "OptimizeForDevices": "Off", "ParentIndex": 2, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblRestrictionType": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "Height", "OnSelect", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblRestrictionType", "OptimizeForDevices": "Off", "ParentIndex": 4, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblSelectedDate": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Align", "Color", "DisabledColor", "FontWeight", "Height", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "OnSelect", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblSelectedDate", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}}, "TopParentName": "DateDetails"}