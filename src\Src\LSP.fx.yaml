"LSP As screen.'phoneLayout_HeaderAndForm_ver3.0'":
    Fill: =RGBA(255,255,255,1)
    LoadingSpinnerColor: =RGBA(211, 66, 9, 1)

    RectQuickActionBar5_1 As rectangle:
        BorderColor: =RGBA(131, 24, 75, 1)
        Fill: =RGBA(211, 66, 9, 1)
        Height: =49.58
        Width: =Parent.Width
        ZIndex: =1

    IconCancel4_1 As icon.Cancel:
        AccessibleLabel: =Self.Tooltip
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        Height: =62
        Icon: =Icon.Cancel
        OnSelect: |
            =Navigate(
              FirstPage,
              ScreenTransition.Fade
            )
        PaddingBottom: =22
        PaddingLeft: =22
        PaddingRight: =22
        PaddingTop: =10
        PressedFill: =RGBA(255, 255, 255, 0.3)
        TabIndex: =0
        Tooltip: ="Cancel item"
        Width: =100
        ZIndex: =2

    LblAppName5_1 As label:
        Color: =RGBA(255, 255, 255, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        Height: =49
        Size: =15.21
        Text: ="Edit the Record"
        Width: =Parent.Width - IconCancel4_1.Width - IconAccept4_1.Width
        Wrap: =false
        X: =88
        ZIndex: =3

    IconAccept4_1 As icon.Check:
        AccessibleLabel: =Self.Tooltip
        BorderColor: =RGBA(131, 24, 75, 1)
        Color: =RGBA(255, 255, 255, 1)
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(244, 244, 244, 1)
        Height: =62
        Icon: =Icon.Check
        OnSelect: |-
            =SubmitForm(LSP_form);
            Back()
        PaddingBottom: =22
        PaddingLeft: =22
        PaddingRight: =22
        PaddingTop: =10
        PressedFill: =RGBA(255, 255, 255, 0.3)
        TabIndex: =0
        Tooltip: ="Submit item"
        Width: =100
        X: =Parent.Width - Self.Width
        ZIndex: =4

    goFirstpage As button:
        BorderThickness: =0
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(0, 0, 0, 0.15)
        FontWeight: =FontWeight.Semibold
        Height: =39.44
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        OnSelect: |
            =
            Navigate(
              FirstPage,
              ScreenTransition.Fade
            )
        PaddingBottom: =2.82
        PaddingLeft: =2.82
        Size: =11.27
        Text: ="Entrance"
        Width: =280
        X: =272
        Y: =5
        ZIndex: =5

    "BrowseGallery1_1 As gallery.'BrowseLayout_Vertical_ThreeTextVariant_ver5.0'":
        '#CopilotOverlayLabel': ="Filtered"
        BorderColor: =RGBA(131, 24, 75, 1)
        DelayItemLoading: =true
        Height: =590
        Items: =SortByColumns([@LSP_CSU], 'Leave From',SortOrder.Ascending)
        Layout: =Layout.Vertical
        LoadingSpinner: =LoadingSpinner.Data
        OnSelect: |-
            =UpdateContext({ selectedRecord: ThisItem })
        ShowScrollbar: =false
        TemplatePadding: =0.00
        TemplateSize: =102
        Width: =798
        Y: =49
        ZIndex: =6

        Title1_1 As label:
            Color: =RGBA(50, 49, 48, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            Font: =Font.Georgia
            FontWeight: =If(ThisItem.IsSelected, FontWeight.Semibold, FontWeight.Normal)
            Height: =34
            OnSelect: =Select(Parent)
            PaddingBottom: =0.00
            PaddingLeft: =0.00
            PaddingRight: =0
            PaddingTop: =0
            Size: =15
            Text: =ThisItem.Name
            VerticalAlign: =VerticalAlign.Top
            Width: =Parent.TemplateWidth - 86
            X: =16
            Y: =2
            ZIndex: =1

        LeaveFrom_1 As label:
            Color: =RGBA(0, 0, 0, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            FontWeight: =If(ThisItem.IsSelected, FontWeight.Semibold, FontWeight.Normal)
            Height: =29
            OnSelect: =Select(Parent)
            PaddingBottom: =0.00
            PaddingLeft: =0.00
            PaddingRight: =0
            PaddingTop: =0
            Size: =10
            Text: =ThisItem.Title
            VerticalAlign: =VerticalAlign.Top
            Width: =Title1_1.Width
            X: =Title1_1.X
            Y: =36
            ZIndex: =2

        LeaveTo_1 As label:
            Color: =RGBA(0, 0, 0, 1)
            DisabledColor: =RGBA(166, 166, 166, 1)
            FontWeight: =If(ThisItem.IsSelected, FontWeight.Semibold, FontWeight.Normal)
            Height: =20
            OnSelect: =Select(Parent)
            PaddingBottom: =0.00
            PaddingLeft: =0.00
            PaddingRight: =0
            PaddingTop: =0
            Size: =10
            Text: =ThisItem.'Leave From'
            VerticalAlign: =VerticalAlign.Top
            Width: =Title1_1.Width
            X: =Title1_1.X
            Y: =65
            ZIndex: =3

        NextArrow1_1 As icon:
            AccessibleLabel: =Self.Tooltip
            BorderColor: =RGBA(131, 24, 75, 1)
            Color: =RGBA(166, 166, 166, 1)
            DisabledBorderColor: =RGBA(166, 166, 166, 1)
            DisabledColor: =RGBA(244, 244, 244, 1)
            Height: =28.17
            Icon: =Icon.ChevronRight
            OnSelect: =Select(Parent)
            PaddingBottom: =9.01
            PaddingLeft: =9.01
            PaddingRight: =16
            PaddingTop: =16
            Tooltip: ="View item details"
            Width: =50
            X: =Parent.TemplateWidth - Self.Width - 12
            Y: =18.59
            ZIndex: =4

        Separator1_1 As rectangle:
            BorderColor: =RGBA(131, 24, 75, 1)
            Fill: =RGBA(255, 255, 255, 1)
            Height: =4.51
            OnSelect: =Select(Parent)
            Width: =Parent.TemplateWidth
            Y: =60.85
            ZIndex: =5

        Rectangle1_1 As rectangle:
            BorderColor: =RGBA(131, 24, 75, 1)
            Fill: =RGBA(131, 24, 75, 1)
            Height: =60.85
            OnSelect: =
            Visible: =ThisItem.IsSelected
            Width: =4
            Y: =0.00
            ZIndex: =6

    Container2_1 As groupContainer.horizontalAutoLayoutContainer:
        DropShadow: =DropShadow.Light
        Height: =590
        LayoutMode: =LayoutMode.Auto
        maximumHeight: =11360
        maximumWidth: =640
        RadiusBottomLeft: =4
        RadiusBottomRight: =4
        RadiusTopLeft: =4
        RadiusTopRight: =4
        Width: =340
        X: =798
        Y: =50
        ZIndex: =7

        LSP_form As form:
            BorderColor: =RGBA(131, 24, 75, 1)
            DataSource: =LSP_CSU
            Item: =selectedRecord
            LayoutMinHeight: =250
            LayoutMinWidth: =250
            Width: =640
            ZIndex: =1

            Approval_DataCard4 As typedDataCard.toggleEditCard:
                BorderColor: =RGBA(131, 24, 75, 1)
                BorderStyle: =BorderStyle.Solid
                DataField: ="Approval"
                Default: =ThisItem.Approval
                DisplayMode: =Parent.DisplayMode
                DisplayName: =DataSourceInfo([@LSP_CSU],DataSourceInfo.DisplayName,Approval)
                Fill: =RGBA(0, 0, 0, 0)
                Height: =50
                Required: =false
                Update: =DataCardValue26.Value
                Width: =342
                X: =0
                Y: =0
                ZIndex: =2

                DataCardKey35 As label:
                    AutoHeight: =true
                    Color: =RGBA(131, 24, 75, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    Height: =48
                    Size: =18
                    Text: =Parent.DisplayName
                    Width: =(Parent.Width - 60) * 0.4
                    Wrap: =false
                    X: =30
                    Y: =10 + (DataCardValue26.Height / 2) - (Self.Height / 2)
                    ZIndex: =1

                DataCardValue26 As toggleSwitch:
                    BorderColor: =If(IsBlank(Parent.Error), Parent.BorderColor, Color.Red)
                    Color: =RGBA(0, 0, 0, 1)
                    Default: =Parent.Default
                    DisplayMode: =Parent.DisplayMode
                    FalseFill: =RGBA(128, 130, 133, 1)
                    FalseText: ="No"
                    HandleFill: =RGBA(255, 255, 255, 1)
                    Height: =32
                    Size: =21
                    Tooltip: =Parent.DisplayName
                    TrueFill: =RGBA(211, 66, 9, 1)
                    TrueText: ="Yes"
                    Width: =134
                    X: =177
                    Y: =12
                    ZIndex: =2

                ErrorMessage29 As label:
                    AutoHeight: =true
                    Color: =RGBA(168, 0, 0, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    FontWeight: =FontWeight.Semibold
                    Height: =10
                    Live: =Live.Assertive
                    PaddingBottom: =0
                    PaddingLeft: =0
                    PaddingRight: =0
                    PaddingTop: =0
                    Size: =24
                    Text: =Parent.Error
                    Visible: =Parent.DisplayMode=DisplayMode.Edit
                    Width: =Parent.Width - 60
                    X: =30
                    Y: =DataCardValue26.Y + DataCardValue26.Height
                    ZIndex: =3

                StarVisible29 As label:
                    Align: =Align.Center
                    Color: =RGBA(131, 24, 75, 1)
                    DisabledColor: =RGBA(166, 166, 166, 1)
                    Height: =DataCardKey35.Height
                    Size: =21
                    Text: ="*"
                    Visible: =And(Parent.Required, Parent.DisplayMode=DisplayMode.Edit)
                    Width: =30
                    Wrap: =false
                    Y: =DataCardKey35.Y
                    ZIndex: =4

    check_availability_2 As button:
        BorderThickness: =0
        DisabledBorderColor: =RGBA(166, 166, 166, 1)
        DisabledColor: =RGBA(166, 166, 166, 1)
        DisabledFill: =RGBA(244, 244, 244, 1)
        Fill: =RGBA(0, 0, 0, 0.15)
        FontWeight: =FontWeight.Semibold
        Height: =39.44
        HoverColor: =RGBA(255, 255, 255, 1)
        HoverFill: =ColorFade(RGBA(211, 66, 9, 1), -20%)
        OnSelect: |
            =Set(varPreviousScreen, "EditRecordScreen");
            Navigate(
              CalendarView,
              ScreenTransition.Fade
            )
        PaddingBottom: =2.82
        PaddingLeft: =2.82
        Size: =11.27
        Text: ="Check Availability"
        Width: =280
        X: =608
        Y: =5
        ZIndex: =8

