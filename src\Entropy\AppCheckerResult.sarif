{"$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.4.json", "runs": [{"columnKind": "utf16CodeUnits", "invocations": [{"executionSuccessful": true}], "results": [{"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconNewItem1.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconNewItem1.OnSelect", "relativeAddress": 0}, "region": {"charLength": 7, "charOffset": 66, "snippet": {"text": "Blank()"}}}, "properties": {"member": "OnSelect", "module": "FirstPage", "type": "FirstPage.IconNewItem1"}}], "message": {"arguments": ["recordToEdit"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-WarnInvalidTypeForVariableDefinition", "ruleIndex": 0}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 7, "snippet": {"text": "User()"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7"}}], "message": {"id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrBadType", "ruleIndex": 1}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 0, "snippet": {"text": "Search"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7"}}], "message": {"arguments": ["Search"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.SearchItems", "relativeAddress": 0}, "region": {"charLength": 5, "charOffset": 40, "snippet": {"text": "Email"}}}, "properties": {"member": "SearchItems", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7"}}], "message": {"arguments": ["Email"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 3}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.galCalendar_1.LblRestrictionInfo.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.galCalendar_1.LblRestrictionInfo.Text", "relativeAddress": 0}, "region": {"charLength": 15, "charOffset": 188, "snippet": {"text": "DateValue(Date)"}}}, "properties": {"member": "Text", "module": "CalendarView", "type": "CalendarView.galCalendar_1.LblRestrictionInfo"}}], "message": {"arguments": ["DateValue"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 4}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.galCalendar_1.LblRestrictionInfo.Text"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.galCalendar_1.LblRestrictionInfo.Text", "relativeAddress": 0}, "region": {"charLength": 1, "charOffset": 204, "snippet": {"text": "="}}}, "properties": {"member": "Text", "module": "CalendarView", "type": "CalendarView.galCalendar_1.LblRestrictionInfo"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 4}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalRestrictions.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalRestrictions.Items", "relativeAddress": 0}, "region": {"charLength": 15, "charOffset": 42, "snippet": {"text": "DateValue(Date)"}}}, "properties": {"member": "Items", "module": "DateDetails", "type": "DateDetails.GalRestrictions"}}], "message": {"arguments": ["DateValue"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 4}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalRestrictions.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalRestrictions.Items", "relativeAddress": 0}, "region": {"charLength": 1, "charOffset": 58, "snippet": {"text": "="}}}, "properties": {"member": "Items", "module": "DateDetails", "type": "DateDetails.GalRestrictions"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 4}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalRestrictions.BtnDeleteRestriction.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalRestrictions.BtnDeleteRestriction.OnSelect", "relativeAddress": 0}, "region": {"charLength": 31, "charOffset": 7, "snippet": {"text": "Calendar_Restriction_Harrisburg"}}}, "properties": {"member": "OnSelect", "module": "DateDetails", "type": "DateDetails.GalRestrictions.BtnDeleteRestriction"}}], "message": {"arguments": ["Calendar_Restriction_Harrisburg"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 3}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalRestrictions.BtnDeleteRestriction.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalRestrictions.BtnDeleteRestriction.OnSelect", "relativeAddress": 0}, "region": {"charLength": 8, "charOffset": 40, "snippet": {"text": "ThisItem"}}}, "properties": {"member": "OnSelect", "module": "DateDetails", "type": "DateDetails.GalRestrictions.BtnDeleteRestriction"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-ErrCollectionDoesNotAcceptThisType", "ruleIndex": 5}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalRestrictions.BtnDeleteRestriction.OnSelect"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalRestrictions.BtnDeleteRestriction.OnSelect", "relativeAddress": 0}, "region": {"charLength": 6, "charOffset": 0, "snippet": {"text": "Remove"}}}, "properties": {"member": "OnSelect", "module": "DateDetails", "type": "DateDetails.GalRestrictions.BtnDeleteRestriction"}}], "message": {"arguments": ["Remove"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalAbsentees.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalAbsentees.Items", "relativeAddress": 0}, "region": {"charLength": 27, "charOffset": 188, "snippet": {"text": "DateTimeValue('Leave From')"}}}, "properties": {"member": "Items", "module": "DateDetails", "type": "DateDetails.GalAbsentees"}}], "message": {"arguments": ["DateTimeValue"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 4}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalAbsentees.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalAbsentees.Items", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 216, "snippet": {"text": "<="}}}, "properties": {"member": "Items", "module": "DateDetails", "type": "DateDetails.GalAbsentees"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 4}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalAbsentees.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalAbsentees.Items", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 225, "snippet": {"text": "&&"}}}, "properties": {"member": "Items", "module": "DateDetails", "type": "DateDetails.GalAbsentees"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 4}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalAbsentees.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalAbsentees.Items", "relativeAddress": 0}, "region": {"charLength": 2, "charOffset": 277, "snippet": {"text": "&&"}}}, "properties": {"member": "Items", "module": "DateDetails", "type": "DateDetails.GalAbsentees"}}], "message": {"arguments": ["Filter"], "id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-SuggestRemoteExecutionHint", "ruleIndex": 4}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "LSP.BrowseGallery1_1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "LSP.BrowseGallery1_1.Items", "relativeAddress": 0}, "region": {"charLength": 12, "charOffset": 26, "snippet": {"text": "'Leave From'"}}}, "properties": {"member": "Items", "module": "LSP", "type": "LSP.BrowseGallery1_1"}}], "message": {"arguments": ["Leave From"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidName", "ruleIndex": 3}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "LSP.BrowseGallery1_1.Items"}], "physicalLocation": {"address": {"fullyQualifiedName": "LSP.BrowseGallery1_1.Items", "relativeAddress": 0}, "region": {"charLength": 13, "charOffset": 0, "snippet": {"text": "SortByColumns"}}}, "properties": {"member": "Items", "module": "LSP", "type": "LSP.BrowseGallery1_1"}}], "message": {"arguments": ["SortByColumns"], "id": "issue"}, "properties": {"level": "High"}, "ruleId": "app-ErrInvalidArgs-Func", "ruleIndex": 2}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DataCardValue5.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DataCardValue5.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.Approval_DataCard1.DataCardValue5"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.Container2.DetailForm1.'Leave Type_DataCard2'.DataCardValue14"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconDelete.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconDelete.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.IconDelete"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconDelete.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconDelete.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "FirstPage", "type": "FirstPage.IconDelete"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconEdit.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconEdit.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.IconEdit"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.IconEdit.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.IconEdit.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "FirstPage", "type": "FirstPage.IconEdit"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.DatePicker_StartFirstPage.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.DatePicker_StartFirstPage.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.DatePicker_StartFirstPage"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.DatePicker_EndFirstPage.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.DatePicker_EndFirstPage.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.DatePicker_EndFirstPage"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.ddApprovalFilter.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.ddApprovalFilter.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.ddApprovalFilter"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.BrowseGallery1.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.BrowseGallery1.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "FirstPage", "type": "FirstPage.BrowseGallery1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Label6.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Label6.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "FirstPage", "type": "FirstPage.Label6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "FirstPage.Label6.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "FirstPage.Label6.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "FirstPage", "type": "FirstPage.Label6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DataCardValue20.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Name_DataCard3.DataCardValue20.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Name_DataCard3.DataCardValue20"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DateValue4.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DateValue4.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.DateValue4"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.HourValue4.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.HourValue4.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.HourValue4"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.MinuteValue4.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.MinuteValue4.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave From_DataCard3'.MinuteValue4"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DateValue5.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DateValue5.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.DateValue5"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.HourValue5.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.HourValue5.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.HourValue5"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.MinuteValue5.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.MinuteValue5.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave To_DataCard3'.MinuteValue5"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.DataCardValue18.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Location_DataCard2.DataCardValue18.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Location_DataCard2.DataCardValue18"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DataCardValue19.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Approval_DataCard3.DataCardValue19.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Approval_DataCard3.DataCardValue19"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Leave Type_DataCard3'.DataCardValue22"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DataCardValue1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DataCardValue1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.Scheduled_DataCard1.DataCardValue1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DateValue6.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DateValue6.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.DateValue6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.HourValue6.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.HourValue6.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.HourValue6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.MinuteValue6.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.MinuteValue6.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Requested Time_DataCard2'.MinuteValue6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.'Created BY_DataCard2'.DataCardValue6"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DataCardValue23.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EditRecordScreen.EditForm4.canceled_DataCard1.DataCardValue23.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EditRecordScreen", "type": "EditRecordScreen.EditForm4.canceled_DataCard1.DataCardValue23"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ShiftStartText.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ShiftStartText.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.ShiftStartText"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ShiftEndText.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ShiftEndText.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.ShiftEndText"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.ComboBox1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.ComboBox1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.ComboBox1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DateValue1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.DateValue1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.DateValue1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.HourValue1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave From_DataCard2'.MinuteValue1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DateValue2.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.DateValue2.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.DateValue2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.HourValue2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Leave To_DataCard2'.MinuteValue2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DataCardValue8.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Approval_DataCard2.DataCardValue8.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Approval_DataCard2.DataCardValue8"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DataCardValue17.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Scheduled_DataCard2.DataCardValue17.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Scheduled_DataCard2.DataCardValue17"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.LeaveTypeDropdown.LeaveTypeDropdownValue"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DataCardValue21.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.canceled_DataCard2.DataCardValue21.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.canceled_DataCard2.DataCardValue21"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.DateValue3.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.DateValue3.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Requested Time_DataCard1'.DateValue3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.HourValue3.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.HourValue3.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Requested Time_DataCard1'.HourValue3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.MinuteValue3.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Requested Time_DataCard1'.MinuteValue3.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Requested Time_DataCard1'.MinuteValue3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DataCardValue12.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Role_DataCard1.DataCardValue12.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Role_DataCard1.DataCardValue12"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.DataCardValue11.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Location_DataCard3.DataCardValue11.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Location_DataCard3.DataCardValue11"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.'Created BY_DataCard1'.DataCardValue7"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DataCardValue16.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Supervisor_DataCard1.DataCardValue16.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Supervisor_DataCard1.DataCardValue16"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DataCardValue15.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Manager_DataCard1.DataCardValue15.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Manager_DataCard1.DataCardValue15"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DataCardValue10.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.EditForm1.Name_DataCard1.DataCardValue10.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.EditForm1.Name_DataCard1.DataCardValue10"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.TextInput2.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.TextInput2.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.TextInput2"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.TextInput3.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.TextInput3.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "EnterData", "type": "EnterData.TextInput3"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.Label6_1.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.Label6_1.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "EnterData", "type": "EnterData.Label6_1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "EnterData.Label6_1.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "EnterData.Label6_1.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "EnterData", "type": "EnterData.Label6_1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.LblAddRestriction.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.LblAddRestriction.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "DateRestriction", "type": "DateRestriction.LblAddRestriction"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.LblAddRestriction.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.LblAddRestriction.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateRestriction", "type": "DateRestriction.LblAddRestriction"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.LblEventsLabel.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.LblEventsLabel.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "DateRestriction", "type": "DateRestriction.LblEventsLabel"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.LblEventsLabel.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.LblEventsLabel.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateRestriction", "type": "DateRestriction.LblEventsLabel"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.ComboEvents.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.ComboEvents.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "DateRestriction", "type": "DateRestriction.ComboEvents"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.LblDateLabel.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.LblDateLabel.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "DateRestriction", "type": "DateRestriction.LblDateLabel"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.LblDateLabel.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.LblDateLabel.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateRestriction", "type": "DateRestriction.LblDateLabel"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.DatePickerRestriction.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.DatePickerRestriction.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "DateRestriction", "type": "DateRestriction.DatePickerRestriction"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.LblTypeLabel.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.LblTypeLabel.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "DateRestriction", "type": "DateRestriction.LblTypeLabel"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.LblTypeLabel.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.LblTypeLabel.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateRestriction", "type": "DateRestriction.LblTypeLabel"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateRestriction.ComboTypeOfEvent.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateRestriction.ComboTypeOfEvent.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "DateRestriction", "type": "DateRestriction.ComboTypeOfEvent"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.LblMonthTitle.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.LblMonthTitle.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "CalendarView", "type": "CalendarView.LblMonthTitle"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.LblMonthTitle.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.LblMonthTitle.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "CalendarView", "type": "CalendarView.LblMonthTitle"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.IconPreviousMonth.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.IconPreviousMonth.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "CalendarView", "type": "CalendarView.IconPreviousMonth"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.IconPreviousMonth.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.IconPreviousMonth.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "CalendarView", "type": "CalendarView.IconPreviousMonth"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.IconNextMonth.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.IconNextMonth.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "CalendarView", "type": "CalendarView.IconNextMonth"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.IconNextMonth.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.IconNextMonth.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "CalendarView", "type": "CalendarView.IconNextMonth"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.GalleryDayHeaders.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.GalleryDayHeaders.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "CalendarView", "type": "CalendarView.GalleryDayHeaders"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.GalleryDayHeaders.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.GalleryDayHeaders.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "CalendarView", "type": "CalendarView.GalleryDayHeaders"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.galCalendar_1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.galCalendar_1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "CalendarView", "type": "CalendarView.galCalendar_1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.galCalendar_1.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.galCalendar_1.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "CalendarView", "type": "CalendarView.galCalendar_1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.galCalendar_1.Label18_1.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.galCalendar_1.Label18_1.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "CalendarView", "type": "CalendarView.galCalendar_1.Label18_1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "CalendarView.galCalendar_1.Label18_1.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "CalendarView.galCalendar_1.Label18_1.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "CalendarView", "type": "CalendarView.galCalendar_1.Label18_1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.LblSelectedDate.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.LblSelectedDate.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "DateDetails", "type": "DateDetails.LblSelectedDate"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.LblSelectedDate.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.LblSelectedDate.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateDetails", "type": "DateDetails.LblSelectedDate"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.LblRestrictionsHeader.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.LblRestrictionsHeader.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "DateDetails", "type": "DateDetails.LblRestrictionsHeader"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.LblRestrictionsHeader.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.LblRestrictionsHeader.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateDetails", "type": "DateDetails.LblRestrictionsHeader"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalRestrictions.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalRestrictions.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "DateDetails", "type": "DateDetails.GalRestrictions"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalRestrictions.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalRestrictions.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateDetails", "type": "DateDetails.GalRestrictions"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.LblNoRestrictions.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.LblNoRestrictions.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "DateDetails", "type": "DateDetails.LblNoRestrictions"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.LblNoRestrictions.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.LblNoRestrictions.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateDetails", "type": "DateDetails.LblNoRestrictions"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.LblAbsenteesHeader.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.LblAbsenteesHeader.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "DateDetails", "type": "DateDetails.LblAbsenteesHeader"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.LblAbsenteesHeader.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.LblAbsenteesHeader.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateDetails", "type": "DateDetails.LblAbsenteesHeader"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalAbsentees.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalAbsentees.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "DateDetails", "type": "DateDetails.GalAbsentees"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.GalAbsentees.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.GalAbsentees.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateDetails", "type": "DateDetails.GalAbsentees"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.LblNoAbsentees.FocusedBorderThickness"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.LblNoAbsentees.FocusedBorderThickness", "relativeAddress": 0}}, "properties": {"member": "FocusedBorderThickness", "module": "DateDetails", "type": "DateDetails.LblNoAbsentees"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-FocusBorderShouldBeVisible", "ruleIndex": 8}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "DateDetails.LblNoAbsentees.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "DateDetails.LblNoAbsentees.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "DateDetails", "type": "DateDetails.LblNoAbsentees"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "LSP.BrowseGallery1_1.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "LSP.BrowseGallery1_1.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "LSP", "type": "LSP.BrowseGallery1_1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "LSP.BrowseGallery1_1.TabIndex"}], "physicalLocation": {"address": {"fullyQualifiedName": "LSP.BrowseGallery1_1.TabIndex", "relativeAddress": 0}}, "properties": {"member": "TabIndex", "module": "LSP", "type": "LSP.BrowseGallery1_1"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-TabIndexShouldBeDefinedForInteractiveControl", "ruleIndex": 7}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "LSP.Container2_1.LSP_form.Approval_DataCard4.DataCardValue26.AccessibleLabel"}], "physicalLocation": {"address": {"fullyQualifiedName": "LSP.Container2_1.LSP_form.Approval_DataCard4.DataCardValue26.AccessibleLabel", "relativeAddress": 0}}, "properties": {"member": "AccessibleLabel", "module": "LSP", "type": "LSP.Container2_1.LSP_form.Approval_DataCard4.DataCardValue26"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "acc-AccessibleLabelNeeded", "ruleIndex": 6}, {"locations": [{"logicalLocations": [{"fullyQualifiedName": "App"}], "physicalLocation": {"address": {"fullyQualifiedName": "App", "relativeAddress": 0}}, "properties": {"module": "App", "type": "App"}}], "message": {"id": "issue"}, "properties": {"level": "Medium"}, "ruleId": "app-DataSourceDefaultMaxRowsLimit", "ruleIndex": 9}], "tool": {"driver": {"fullName": "PowerApps app checker", "name": "PowerApps app checker", "rules": [{"id": "app-WarnInvalidTypeForVariableDefinition", "messageStrings": {"issue": {"text": "The variable {0} needs to have a non-Blank type. If this is the only place that {0} is defined, resolve this by adding another definition elsewhere that assigns a non-Blank value to this variable."}}, "properties": {"componentType": "app", "level": "Medium", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrBadType", "messageStrings": {"issue": {"text": "Invalid argument type."}}, "properties": {"componentType": "app", "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrInvalidArgs-Func", "messageStrings": {"issue": {"text": "The function '{0}' has some invalid arguments."}}, "properties": {"componentType": "app", "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrInvalidName", "messageStrings": {"issue": {"text": "Name isn't valid. '{0}' isn't recognized."}}, "properties": {"componentType": "app", "howToFix": ["Remove or correct the reference to the name that isn't valid."], "level": "High", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-SuggestRemoteExecutionHint", "messageStrings": {"issue": {"text": "Delegation warning. The \"{0}\" part of this formula might not work correctly on large data sets."}}, "properties": {"componentType": "app", "howToFix": ["If your data set exceeds the 500 record limit but contains less than 2,000 records, try resetting the limit.", "Try simplifying the formula.", "Try moving your data to a different data source."], "level": "Medium", "primaryCategory": "formula", "whyFix": ""}}, {"id": "app-ErrCollectionDoesNotAcceptThisType", "messageStrings": {"issue": {"text": "Incompatible type. The collection can't contain values of this type."}}, "properties": {"componentType": "app", "howToFix": ["You might need to convert the type of the item using, for example, a Datevalue, Text, or Value function."], "level": "Medium", "primaryCategory": "formula", "whyFix": ""}}, {"id": "acc-AccessibleLabelNeeded", "messageStrings": {"issue": {"text": "Missing accessible label"}}, "properties": {"componentType": "app", "howToFix": ["Edit the accessible label property to describe the item."], "level": "Medium", "primaryCategory": "accessibility", "whyFix": "If there's no accessible text, people who can’t see the screen won't understand what’s in images and controls."}}, {"id": "acc-TabIndexShouldBeDefinedForInteractiveControl", "messageStrings": {"issue": {"text": "Missing tab stop"}}, "properties": {"componentType": "app", "howToFix": ["Set TabIndex to 0 or greater to ensure that interactive elements have a tab stop."], "level": "Medium", "primaryCategory": "accessibility", "whyFix": "People who use the keyboard with your app will not be able to access this element without a tab stop."}}, {"id": "acc-FocusBorderShouldBeVisible", "messageStrings": {"issue": {"text": "Focus isn't showing"}}, "properties": {"componentType": "app", "howToFix": ["Change the FocusedBorderThickness property to be more than 0."], "level": "Medium", "primaryCategory": "accessibility", "whyFix": "If the focus isn't visible, people who don't use a mouse won't be able to see it when they're interacting with the app."}}, {"id": "app-DataSourceDefaultMaxRowsLimit", "messageStrings": {"issue": {"text": "Data row limit for non-delegable queries is more than 500"}}, "properties": {"componentType": "app", "howToFix": ["Update 'Data row limit for non-delegable queries' in the advanced app settings."], "level": "Medium", "primaryCategory": "performance", "whyFix": "A 'data row limit' more than 500 may result in a slower app when running non-delegable queries, because the app will have to process every row itself, rather than running the query on the server."}}], "version": "1.346"}}}], "version": "2.1.0"}